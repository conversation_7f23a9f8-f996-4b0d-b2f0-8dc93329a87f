'use client';

import React, { useState } from 'react';
import Logo from './Logo/Logo';
import Navigation from './Navigation/Navigation';
import HeaderButtons from './HeaderButtons/HeaderButtons';
import { HeaderProps, NavigationItem } from '@/types/header';
import styles from './Header.module.css';

/**
 * Main Header component that combines Logo, Navigation, and HeaderButtons
 * Responsive design with mobile hamburger menu
 */
const Header: React.FC<HeaderProps> = ({
  className = '',
  navigationItems,
  onDonateClick,
  onJoinClick,
  onLogoClick
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleNavigationItemClick = (item: NavigationItem) => {
    if (item.href) {
      // Handle navigation - you can integrate with Next.js router here
      console.log('Navigate to:', item.href);
    }
    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  const handleLogoClick = () => {
    onLogoClick?.();
    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header
      className={`${styles.header} ${className}`}
      role="banner"
    >
      <div className={styles.container}>
        <div className={styles.headerContent}>
          {/* Logo */}
          <Logo
            onClick={handleLogoClick}
            className={styles.logoContainer}
          />

          {/* Desktop Navigation */}
          <Navigation
            items={navigationItems}
            onItemClick={handleNavigationItemClick}
            className={styles.navigationContainer}
          />

          {/* Desktop Header Buttons */}
          <HeaderButtons
            onDonateClick={onDonateClick}
            onJoinClick={onJoinClick}
            className={styles.buttonsContainer}
          />

          {/* Mobile Menu Button - 2-line hamburger with X animation */}
          <button
            onClick={toggleMobileMenu}
            className={`${styles.mobileMenuButton} ${isMobileMenuOpen ? styles.mobileMenuButtonOpen : ''}`}
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle mobile menu"
          >
            <div className={styles.hamburgerIcon}>
              <span className={`${styles.hamburgerLine} ${styles.hamburgerLineTop}`}></span>
              <span className={`${styles.hamburgerLine} ${styles.hamburgerLineBottom}`}></span>
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`${styles.mobileMenu} ${isMobileMenuOpen ? styles.mobileMenuOpen : ''}`}>
          <nav className={styles.mobileNavigation} role="navigation" aria-label="Mobile navigation">
              {/* Mobile Navigation Items */}
              <div className={styles.mobileNavItems}>
                <Navigation
                  items={navigationItems}
                  onItemClick={handleNavigationItemClick}
                  className="mobileNavigation"
                />
              </div>

              {/* Mobile Action Buttons */}
              <div className={styles.mobileButtonsContainer}>
                <HeaderButtons
                  onDonateClick={onDonateClick}
                  onJoinClick={onJoinClick}
                  isMobile={true}
                />
              </div>
            </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
