'use client';

import React, { useState } from 'react';
import Logo from './Logo/Logo';
import Navigation from './Navigation/Navigation';
import HeaderButtons from './HeaderButtons/HeaderButtons';
import { HeaderProps, NavigationItem } from '@/types/header';

// Default navigation items based on the design
const defaultNavigationItems: NavigationItem[] = [
  {
    label: 'WHAT WE DO',
    hasDropdown: true,
    dropdownItems: [
      { label: 'Investment Services', href: '/services/investment' },
      { label: 'Portfolio Management', href: '/services/portfolio' },
      { label: 'Financial Planning', href: '/services/planning' },
      { label: 'Wealth Advisory', href: '/services/advisory' },
    ]
  },
  {
    label: 'WHO WE ARE',
    hasDropdown: true,
    dropdownItems: [
      { label: 'About Us', href: '/about' },
      { label: 'Our Team', href: '/team' },
      { label: 'Leadership', href: '/leadership' },
      { label: 'History', href: '/history' },
    ]
  },
  {
    label: 'ENDOWMENT FUND',
    href: '/endowment'
  },
  {
    label: 'GRAND CONNECT',
    href: '/connect'
  },
  {
    label: 'IMPACT & LEGACY',
    href: '/impact'
  }
];

/**
 * Main Header component that combines Logo, Navigation, and HeaderButtons
 * Responsive design with mobile hamburger menu
 */
const Header: React.FC<HeaderProps> = ({
  className = '',
  navigationItems = defaultNavigationItems,
  onDonateClick,
  onJoinClick,
  onLogoClick
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleNavigationItemClick = (item: NavigationItem) => {
    if (item.href) {
      // Handle navigation - you can integrate with Next.js router here
      console.log('Navigate to:', item.href);
    }
    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  const handleLogoClick = () => {
    onLogoClick?.();
    // Close mobile menu if open
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header 
      className={`bg-white border-b border-gray-200 sticky top-0 z-40 ${className}`}
      role="banner"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Logo 
            onClick={handleLogoClick}
            className="flex-shrink-0"
          />

          {/* Desktop Navigation */}
          <Navigation 
            items={navigationItems}
            onItemClick={handleNavigationItemClick}
            className="flex-1 justify-center"
          />

          {/* Desktop Header Buttons */}
          <HeaderButtons 
            onDonateClick={onDonateClick}
            onJoinClick={onJoinClick}
            className="hidden md:flex flex-shrink-0"
          />

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMobileMenu}
            className="md:hidden p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle mobile menu"
          >
            <svg 
              className="w-6 h-6" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="space-y-4" role="navigation" aria-label="Mobile navigation">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  <button
                    onClick={() => handleNavigationItemClick(item)}
                    className="block w-full text-left px-4 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-50 font-medium text-sm tracking-wide"
                  >
                    {item.label}
                  </button>
                  {/* Mobile dropdown items */}
                  {item.hasDropdown && item.dropdownItems && (
                    <div className="ml-4 mt-2 space-y-2">
                      {item.dropdownItems.map((dropdownItem) => (
                        <button
                          key={dropdownItem.label}
                          onClick={() => handleNavigationItemClick(dropdownItem)}
                          className="block w-full text-left px-4 py-1 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                        >
                          {dropdownItem.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              {/* Mobile Action Buttons */}
              <div className="pt-4 border-t border-gray-200">
                <HeaderButtons 
                  onDonateClick={onDonateClick}
                  onJoinClick={onJoinClick}
                  className="flex flex-col space-y-3 space-x-0"
                />
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
