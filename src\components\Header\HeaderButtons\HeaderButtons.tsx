'use client';

import React from 'react';
import { HeaderButtonsProps } from '@/types/header';

/**
 * HeaderButtons component containing action buttons (Donate and Join Now)
 * Features distinct styling for primary and secondary actions
 */
const HeaderButtons: React.FC<HeaderButtonsProps> = ({ 
  className = '', 
  onDonateClick, 
  onJoinClick 
}) => {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Donate Button - Dark/Primary Style */}
      <button
        onClick={onDonateClick}
        className={`
          px-6 py-2 bg-gray-800 text-white font-medium text-sm tracking-wide
          hover:bg-gray-900 active:bg-gray-700
          focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
          transition-all duration-200 ease-in-out
          rounded-sm
        `}
        aria-label="Make a donation"
      >
        DONATE
      </button>

      {/* Join Now Button - Light Blue/Secondary Style */}
      <button
        onClick={onJoinClick}
        className={`
          px-6 py-2 bg-blue-400 text-white font-medium text-sm tracking-wide
          hover:bg-blue-500 active:bg-blue-300
          focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2
          transition-all duration-200 ease-in-out
          rounded-sm
        `}
        aria-label="Join our community"
      >
        JOIN NOW
      </button>
    </div>
  );
};

export default HeaderButtons;
