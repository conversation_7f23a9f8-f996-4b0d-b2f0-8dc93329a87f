'use client';

import React from 'react';
import Image from 'next/image';
import { LogoProps } from '@/types/header';
import styles from './Logo.module.css';

/**
 * Logo component for Kriptaz Invest
 * Features responsive logo switching between desktop and mobile versions
 */
const Logo: React.FC<LogoProps> = ({
  className = '',
  onClick
}) => {
  return (
    <div
      className={`${styles.logoContainer} ${className}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && onClick) {
          e.preventDefault();
          onClick();
        }
      }}
      aria-label="Kriptaz Invest - Go to homepage"
    >
      {/* Desktop Logo - Full Logo */}
      <Image
        src="/logos/kriptaz-invest-full-black-logo.svg"
        alt="Kriptaz Invest"
        width={200}
        height={40}
        className={`${styles.logoDesktop} ${styles.logoImage}`}
        priority
      />

      {/* Mobile Logo - Icon Only */}
      <Image
        src="/logos/kriptaz-invest-icon-black-logo.svg"
        alt="Kriptaz Invest"
        width={32}
        height={32}
        className={`${styles.logoMobile} ${styles.logoImage}`}
        priority
      />
    </div>
  );
};

export default Logo;
