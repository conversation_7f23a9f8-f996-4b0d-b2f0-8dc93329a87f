'use client';

import React from 'react';
import { LogoProps } from '@/types/header';

/**
 * Logo component for the Grand Founders brand
 * Features the distinctive sunburst icon with company name
 */
const Logo: React.FC<LogoProps> = ({ 
  className = '', 
  onClick 
}) => {
  return (
    <div 
      className={`flex items-center space-x-3 cursor-pointer ${className}`}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && onClick) {
          e.preventDefault();
          onClick();
        }
      }}
      aria-label="Grand Founders - Go to homepage"
    >
      {/* Sunburst Icon */}
      <div className="w-8 h-8 flex items-center justify-center">
        <svg 
          width="32" 
          height="32" 
          viewBox="0 0 32 32" 
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
          className="text-gray-800"
          aria-hidden="true"
        >
          {/* Sunburst rays */}
          <g stroke="currentColor" strokeWidth="1.5" strokeLinecap="round">
            {/* Top rays */}
            <line x1="16" y1="2" x2="16" y2="6" />
            <line x1="22.36" y1="4.64" x2="19.78" y2="7.22" />
            <line x1="25.36" y1="9.64" x2="21.78" y2="10.22" />
            <line x1="27" y1="16" x2="23" y2="16" />
            <line x1="25.36" y1="22.36" x2="21.78" y2="21.78" />
            <line x1="22.36" y1="27.36" x2="19.78" y2="24.78" />
            <line x1="16" y1="30" x2="16" y2="26" />
            <line x1="9.64" y1="27.36" x2="12.22" y2="24.78" />
            <line x1="6.64" y1="22.36" x2="10.22" y2="21.78" />
            <line x1="5" y1="16" x2="9" y2="16" />
            <line x1="6.64" y1="9.64" x2="10.22" y2="10.22" />
            <line x1="9.64" y1="4.64" x2="12.22" y2="7.22" />
          </g>
          {/* Center circle */}
          <circle 
            cx="16" 
            cy="16" 
            r="6" 
            fill="currentColor"
          />
        </svg>
      </div>
      
      {/* Company Name */}
      <div className="flex flex-col">
        <span className="text-lg font-bold text-gray-800 leading-tight">
          Grand
        </span>
        <span className="text-lg font-bold text-gray-800 leading-tight -mt-1">
          Founders
        </span>
      </div>
    </div>
  );
};

export default Logo;
