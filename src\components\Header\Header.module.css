/* Header Component Styles */

.header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-light-gray);
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  font-family: var(--font-primary);
}

.container {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logoContainer {
  flex-shrink: 0;
}

.navigationContainer {
  flex: 1;
  justify-content: center;
}

.buttonsContainer {
  display: none;
  flex-shrink: 0;
}

@media (min-width: 768px) {
  .buttonsContainer {
    display: flex;
  }
}

.mobileMenuButton {
  display: block;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

@media (min-width: 768px) {
  .mobileMenuButton {
    display: none;
  }
}

.mobileMenuButton:hover {
  color: var(--color-gray-900);
  background-color: var(--color-light-gray);
}

.mobileMenuButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
}

/* Hamburger Icon Container */
.hamburgerIcon {
  width: 1.5rem;
  height: 1.125rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Hamburger Lines */
.hamburgerLine {
  width: 100%;
  height: 3px;
  background-color: currentColor;
  border-radius: 2px;
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

.hamburgerLineTop {
  transform: translateY(0) rotate(0deg);
}

.hamburgerLineBottom {
  transform: translateY(0) rotate(0deg);
}

/* X Animation when menu is open */
.mobileMenuButtonOpen .hamburgerLineTop {
  transform: translateY(9px) rotate(45deg);
}

.mobileMenuButtonOpen .hamburgerLineBottom {
  transform: translateY(-9px) rotate(-45deg);
}

.mobileMenu {
  display: block;
  border-top: 1px solid var(--color-light-gray);
  padding: 1rem 0;
}

@media (min-width: 768px) {
  .mobileMenu {
    display: none;
  }
}

.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileNavItems {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileButtonsContainer {
  padding-top: 1rem;
  border-top: 1px solid var(--color-light-gray);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
