/* Header Component Styles */

.header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-light-gray);
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  font-family: var(--font-primary);
}

.container {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logoContainer {
  flex-shrink: 0;
}

.navigationContainer {
  flex: 1;
  justify-content: center;
}

.buttonsContainer {
  display: none;
  flex-shrink: 0;
}

@media (min-width: 768px) {
  .buttonsContainer {
    display: flex;
  }
}

.mobileMenuButton {
  display: block;
  padding: 0.5rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}

@media (min-width: 768px) {
  .mobileMenuButton {
    display: none;
  }
}

.mobileMenuButton:hover {
  color: var(--color-gray-900);
}

.mobileMenuButton:focus {
  outline: none;
  /* Removed focus outline as requested */
}

/* Hamburger Icon Container */
.hamburgerIcon {
  width: 1.75rem;
  height: 1.125rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Hamburger Lines */
.hamburgerLine {
  width: 100%;
  height: 3px;
  background-color: currentColor;
  border-radius: 2px;
  transition: all 0.3s ease-in-out;
  transform-origin: center center;
  position: absolute;
}

.hamburgerLineTop {
  transform: translateY(-4.5px) rotate(0deg);
}

.hamburgerLineBottom {
  transform: translateY(4.5px) rotate(0deg);
}

/* Perfect X Animation when menu is open */
.mobileMenuButtonOpen .hamburgerLineTop {
  transform: translateY(0) rotate(45deg);
}

.mobileMenuButtonOpen .hamburgerLineBottom {
  transform: translateY(0) rotate(-45deg);
}

.mobileMenu {
  display: block;
  background-color: var(--color-primary-black);
  border-top: 1px solid var(--color-light-gray);
  padding: 1.5rem 0;
  margin-top: 0;
}

@media (min-width: 768px) {
  .mobileMenu {
    display: none;
  }
}

.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0 1rem;
}

.mobileNavItems {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobileButtonsContainer {
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}
