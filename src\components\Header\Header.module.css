/* Header Component Styles */

.header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-light-gray);
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  font-family: var(--font-primary);
}

.container {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logoContainer {
  flex-shrink: 0;
}

.navigationContainer {
  flex: 1;
  justify-content: center;
}

.buttonsContainer {
  display: none;
  flex-shrink: 0;
}

@media (min-width: 768px) {
  .buttonsContainer {
    display: flex;
  }
}

.mobileMenuButton {
  display: block;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

@media (min-width: 768px) {
  .mobileMenuButton {
    display: none;
  }
}

.mobileMenuButton:hover {
  color: var(--color-gray-900);
  background-color: var(--color-light-gray);
}

.mobileMenuButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
}

.mobileMenuIcon {
  width: 1.5rem;
  height: 1.5rem;
}

.mobileMenu {
  display: block;
  border-top: 1px solid var(--color-light-gray);
  padding: 1rem 0;
}

@media (min-width: 768px) {
  .mobileMenu {
    display: none;
  }
}

.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileNavItem {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  font-family: var(--font-primary);
}

.mobileNavItem:hover {
  color: var(--color-gray-900);
  background-color: var(--color-light-gray);
}

.mobileDropdownItems {
  margin-left: 1rem;
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileDropdownItem {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.25rem 1rem;
  font-size: 0.875rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  font-family: var(--font-primary);
}

.mobileDropdownItem:hover {
  color: var(--color-gray-800);
  background-color: var(--color-light-gray);
}

.mobileButtonsContainer {
  padding-top: 1rem;
  border-top: 1px solid var(--color-light-gray);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
