/* Navigation Component Styles - Compact Design */

.navigation {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-family: var(--font-primary);
}

@media (min-width: 768px) {
  .navigation {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.25rem; /* Reduced from 2rem for more compact spacing */
  }
}

.navigationButton {
  color: var(--color-white); /* White text for mobile dark background */
  background: none;
  border: none;
  font-weight: 400; /* Reduced from 500 for thinner appearance */
  font-size: 1rem; /* Slightly larger for mobile readability */
  letter-spacing: 0.015em; /* Reduced letter spacing */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  font-family: var(--font-primary);
  padding: 1rem 1.5rem; /* Increased padding for better touch targets */
  white-space: nowrap;
  width: 100%; /* Full width on mobile */
  text-align: left; /* Left align on mobile */
  border-radius: 0.375rem;
  margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .navigationButton {
    color: var(--color-gray-700); /* Gray text for desktop light background */
    font-size: 0.8125rem; /* Reduced from 0.875rem for more compact desktop */
    padding: 0.375rem 0.75rem; /* Compact desktop padding */
    width: auto; /* Auto width on desktop */
    text-align: center; /* Center align on desktop */
    margin-bottom: 0;
    border-radius: 0.125rem;
  }
}

.navigationButton:hover {
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.1); /* Subtle white overlay for mobile */
}

@media (min-width: 768px) {
  .navigationButton:hover {
    color: var(--color-gray-900);
    background-color: var(--color-light-gray);
  }
}

.navigationButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.375rem;
}

@media (min-width: 768px) {
  .navigationButton:focus {
    border-radius: 0.125rem;
  }
}
