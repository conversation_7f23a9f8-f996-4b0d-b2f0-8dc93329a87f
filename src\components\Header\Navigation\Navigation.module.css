/* Navigation Component Styles - Compact Design */

.navigation {
  display: none; /* Hidden by default on mobile */
  font-family: var(--font-primary);
}

/* Desktop navigation - visible only on desktop */
@media (min-width: 768px) {
  .navigation {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.5rem; /* Increased for better spacing */
  }
}

/* Mobile navigation - only visible when used in mobile menu context */
.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-family: var(--font-primary);
}

/* Desktop navigation button styles */
.navigationButton {
  color: var(--color-gray-700);
  background: none;
  border: none;
  font-weight: 500; /* Increased for better visibility */
  font-size: 0.9375rem; /* Increased from 0.8125rem for better readability */
  letter-spacing: 0.02em;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  font-family: var(--font-primary);
  padding: 0.5rem 1rem; /* Increased padding for better click targets */
  white-space: nowrap;
  border-radius: 0.25rem;
}

/* Mobile navigation button styles - only for mobile menu context */
.mobileNavigation .navigationButton {
  color: var(--color-white);
  font-size: 1.125rem; /* Slightly larger for full-screen menu */
  font-weight: 400;
  padding: 1.25rem 1.5rem; /* Increased padding for better touch targets */
  width: 100%;
  text-align: left;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  letter-spacing: 0.02em;
}

/* Desktop hover and focus states */
.navigationButton:hover {
  color: var(--color-gray-900);
  background-color: var(--color-light-gray);
}

.navigationButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.25rem;
}

/* Mobile navigation hover and focus states */
.mobileNavigation .navigationButton:hover {
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.1);
}

.mobileNavigation .navigationButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.375rem;
}
