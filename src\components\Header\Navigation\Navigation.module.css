/* Navigation Component Styles - Compact Design */

.navigation {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-family: var(--font-primary);
}

@media (min-width: 768px) {
  .navigation {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.25rem; /* Reduced from 2rem for more compact spacing */
  }
}

.navigationButton {
  color: var(--color-gray-700);
  background: none;
  border: none;
  font-weight: 400; /* Reduced from 500 for thinner appearance */
  font-size: 0.875rem; /* Mobile size */
  letter-spacing: 0.015em; /* Reduced letter spacing */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  font-family: var(--font-primary);
  padding: 0.75rem 1rem; /* Mobile padding */
  white-space: nowrap;
  width: 100%; /* Full width on mobile */
  text-align: left; /* Left align on mobile */
  border-radius: 0.25rem;
}

@media (min-width: 768px) {
  .navigationButton {
    font-size: 0.8125rem; /* Reduced from 0.875rem for more compact desktop */
    padding: 0.375rem 0.75rem; /* Compact desktop padding */
    width: auto; /* Auto width on desktop */
    text-align: center; /* Center align on desktop */
  }
}

.navigationButton:hover {
  color: var(--color-gray-900);
  background-color: var(--color-light-gray);
}

.navigationButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.125rem;
}
