/* Navigation Component Styles */

.navigation {
  display: none;
  align-items: center;
  gap: 2rem;
  font-family: var(--font-primary);
}

@media (min-width: 768px) {
  .navigation {
    display: flex;
  }
}

.navigationItem {
  position: relative;
}

.navigationButton {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: color 0.2s ease-in-out;
  cursor: pointer;
  font-family: var(--font-primary);
}

.navigationButton:hover {
  color: var(--color-gray-900);
}

.navigationButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.125rem;
}

.navigationButtonActive {
  color: var(--color-gray-900);
}

.dropdownIcon {
  width: 0.75rem;
  height: 0.75rem;
  transition: transform 0.2s ease-in-out;
}

.dropdownIconRotated {
  transform: rotate(180deg);
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  width: 12rem;
  background-color: var(--color-white);
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-light-gray);
  padding: 0.25rem 0;
  z-index: 50;
}

.dropdownMenuItem {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  font-family: var(--font-primary);
}

.dropdownMenuItem:hover {
  background-color: var(--color-light-gray);
  color: var(--color-gray-900);
}
