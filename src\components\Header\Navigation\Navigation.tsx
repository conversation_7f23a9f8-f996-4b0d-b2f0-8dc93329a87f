'use client';

import React, { useState, useRef, useEffect } from 'react';
import { NavigationProps, NavigationItem } from '@/types/header';
import styles from './Navigation.module.css';

// Default navigation items moved from Header component
const defaultNavigationItems: NavigationItem[] = [
  {
    label: 'WHAT WE DO',
    hasDropdown: true,
    dropdownItems: [
      { label: 'Investment Services', href: '/services/investment' },
      { label: 'Portfolio Management', href: '/services/portfolio' },
      { label: 'Financial Planning', href: '/services/planning' },
      { label: 'Wealth Advisory', href: '/services/advisory' },
    ]
  },
  {
    label: 'WHO WE ARE',
    hasDropdown: true,
    dropdownItems: [
      { label: 'About Us', href: '/about' },
      { label: 'Our Team', href: '/team' },
      { label: 'Leadership', href: '/leadership' },
      { label: 'History', href: '/history' },
    ]
  },
  {
    label: 'ENDOWMENT FUND',
    href: '/endowment'
  },
  {
    label: 'GRAND CONNECT',
    href: '/connect'
  },
  {
    label: 'IMPACT & LEGACY',
    href: '/impact'
  }
];

/**
 * Navigation component with dropdown functionality
 * Handles keyboard navigation and accessibility
 */
const Navigation: React.FC<NavigationProps> = ({
  items = defaultNavigationItems,
  className = '',
  onItemClick
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown) {
        const dropdownElement = dropdownRefs.current[openDropdown];
        if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
          setOpenDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openDropdown]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  const handleItemClick = (item: NavigationItem) => {
    if (item.hasDropdown) {
      setOpenDropdown(openDropdown === item.label ? null : item.label);
    } else {
      setOpenDropdown(null);
      onItemClick?.(item);
    }
  };

  const handleDropdownItemClick = (item: NavigationItem) => {
    setOpenDropdown(null);
    onItemClick?.(item);
  };

  return (
    <nav
      className={`${styles.navigation} ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      {items.map((item) => (
        <div
          key={item.label}
          className={styles.navigationItem}
          ref={(el) => {
            if (el) dropdownRefs.current[item.label] = el;
          }}
        >
          <button
            className={`${styles.navigationButton} ${
              openDropdown === item.label ? styles.navigationButtonActive : ''
            }`}
            onClick={() => handleItemClick(item)}
            aria-expanded={item.hasDropdown ? openDropdown === item.label : undefined}
            aria-haspopup={item.hasDropdown ? 'menu' : undefined}
          >
            <span>{item.label}</span>
            {item.hasDropdown && (
              <svg
                className={`${styles.dropdownIcon} ${
                  openDropdown === item.label ? styles.dropdownIconRotated : ''
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            )}
          </button>

          {/* Dropdown Menu */}
          {item.hasDropdown && item.dropdownItems && openDropdown === item.label && (
            <div
              className={styles.dropdownMenu}
              role="menu"
              aria-orientation="vertical"
            >
              {item.dropdownItems.map((dropdownItem) => (
                <button
                  key={dropdownItem.label}
                  className={styles.dropdownMenuItem}
                  onClick={() => handleDropdownItemClick(dropdownItem)}
                  role="menuitem"
                >
                  {dropdownItem.label}
                </button>
              ))}
            </div>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Navigation;
