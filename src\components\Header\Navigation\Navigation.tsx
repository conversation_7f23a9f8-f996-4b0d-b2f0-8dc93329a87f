'use client';

import React, { useState, useRef, useEffect } from 'react';
import { NavigationProps, NavigationItem } from '@/types/header';

/**
 * Navigation component with dropdown functionality
 * Handles keyboard navigation and accessibility
 */
const Navigation: React.FC<NavigationProps> = ({ 
  items, 
  className = '', 
  onItemClick 
}) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown) {
        const dropdownElement = dropdownRefs.current[openDropdown];
        if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
          setOpenDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openDropdown]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  const handleItemClick = (item: NavigationItem) => {
    if (item.hasDropdown) {
      setOpenDropdown(openDropdown === item.label ? null : item.label);
    } else {
      setOpenDropdown(null);
      onItemClick?.(item);
    }
  };

  const handleDropdownItemClick = (item: NavigationItem) => {
    setOpenDropdown(null);
    onItemClick?.(item);
  };

  return (
    <nav 
      className={`hidden md:flex items-center space-x-8 ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      {items.map((item) => (
        <div 
          key={item.label}
          className="relative"
          ref={(el) => {
            if (el) dropdownRefs.current[item.label] = el;
          }}
        >
          <button
            className={`
              flex items-center space-x-1 text-gray-700 hover:text-gray-900 
              font-medium text-sm tracking-wide transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm
              ${openDropdown === item.label ? 'text-gray-900' : ''}
            `}
            onClick={() => handleItemClick(item)}
            aria-expanded={item.hasDropdown ? openDropdown === item.label : undefined}
            aria-haspopup={item.hasDropdown ? 'menu' : undefined}
          >
            <span>{item.label}</span>
            {item.hasDropdown && (
              <svg 
                className={`w-3 h-3 transition-transform duration-200 ${
                  openDropdown === item.label ? 'rotate-180' : ''
                }`}
                fill="currentColor" 
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path 
                  fillRule="evenodd" 
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                  clipRule="evenodd" 
                />
              </svg>
            )}
          </button>

          {/* Dropdown Menu */}
          {item.hasDropdown && item.dropdownItems && openDropdown === item.label && (
            <div 
              className="absolute top-full left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50"
              role="menu"
              aria-orientation="vertical"
            >
              {item.dropdownItems.map((dropdownItem) => (
                <button
                  key={dropdownItem.label}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150"
                  onClick={() => handleDropdownItemClick(dropdownItem)}
                  role="menuitem"
                >
                  {dropdownItem.label}
                </button>
              ))}
            </div>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Navigation;
