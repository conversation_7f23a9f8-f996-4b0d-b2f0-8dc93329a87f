/* Logo Component Styles */

.logoContainer {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-family: var(--font-primary);
}

.logoContainer:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-green);
  border-radius: 0.25rem;
}

.logoDesktop {
  display: none;
  height: 2.5rem;
  width: auto;
}

@media (min-width: 768px) {
  .logoDesktop {
    display: block;
  }
}

.logoMobile {
  display: block;
  height: 2rem;
  width: auto;
}

@media (min-width: 768px) {
  .logoMobile {
    display: none;
  }
}

.logoImage {
  object-fit: contain;
  filter: brightness(0) saturate(100%); /* Makes SVG black */
}
