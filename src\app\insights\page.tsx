'use client';

import { Header } from '@/components/Header';

export default function InsightsPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Insights
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Market insights and investment analysis
          </p>
          <div className="max-w-4xl mx-auto text-left">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-3">Market Analysis</h2>
                <p className="text-gray-600 mb-4">
                  Stay informed with our latest market analysis and trends that 
                  could impact your investment portfolio.
                </p>
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Read More →
                </a>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-3">Investment Strategies</h2>
                <p className="text-gray-600 mb-4">
                  Discover proven investment strategies and approaches that can 
                  help optimize your portfolio performance.
                </p>
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Read More →
                </a>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-3">Economic Outlook</h2>
                <p className="text-gray-600 mb-4">
                  Get expert perspectives on economic trends and their potential 
                  impact on various investment sectors.
                </p>
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Read More →
                </a>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h2 className="text-xl font-semibold mb-3">Portfolio Tips</h2>
                <p className="text-gray-600 mb-4">
                  Learn practical tips for portfolio diversification and risk 
                  management from our investment experts.
                </p>
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Read More →
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
