'use client';

import { Header } from '@/components/Header';

export default function DonatePage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Donate
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Support our mission and make a difference
          </p>
          <div className="max-w-3xl mx-auto">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h2 className="text-2xl font-semibold mb-6">Make a Donation</h2>
              <p className="text-gray-600 mb-8">
                Your donation helps us continue our mission of providing excellent 
                investment services and supporting financial education initiatives.
              </p>
              
              <div className="grid gap-4 md:grid-cols-3 mb-8">
                <button className="bg-blue-100 hover:bg-blue-200 text-blue-800 font-semibold py-4 px-6 rounded-lg transition-colors">
                  $50
                </button>
                <button className="bg-blue-100 hover:bg-blue-200 text-blue-800 font-semibold py-4 px-6 rounded-lg transition-colors">
                  $100
                </button>
                <button className="bg-blue-100 hover:bg-blue-200 text-blue-800 font-semibold py-4 px-6 rounded-lg transition-colors">
                  $250
                </button>
              </div>
              
              <form className="space-y-4">
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                    Custom Amount ($)
                  </label>
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter amount"
                  />
                </div>
                
                <div>
                  <label htmlFor="donor-name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="donor-name"
                    name="donor-name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="donor-email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="donor-email"
                    name="donor-email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="anonymous"
                    name="anonymous"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="anonymous" className="ml-2 block text-sm text-gray-700">
                    Make this donation anonymous
                  </label>
                </div>
                
                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-semibold"
                >
                  Donate Now
                </button>
              </form>
              
              <div className="mt-8 text-sm text-gray-500">
                <p>
                  Your donation is secure and helps support our financial education 
                  and investment services. Thank you for your generosity.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
